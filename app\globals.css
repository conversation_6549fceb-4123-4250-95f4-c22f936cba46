@import "tailwindcss";


/* ===============================
   🌗 Custom Color and Font Variables
   =============================== */

:root {
  --color-background: #ffffff;
  --color-foreground: #171717;

  --font-mono: var(--font-inter);
}

/* ===============================
   🌒 Dark Mode Variables
   =============================== */

@media (prefers-color-scheme: dark) {
  :root {
    --color-background: #0a0a0a;
    --color-foreground: #ededed;
  }
}

/* ===============================
   🧱 Global Base Styles
   =============================== */

html {
  scroll-behavior: smooth;
}

* {
  font-family: var(--font-inter);
}

body {
  background-color: var(--color-background);
  color: var(--color-foreground);
  font-family: var(--font-inter);
  transition: background-color 0.3s, color 0.3s;
}

/* ===============================
   🎭 Smooth Animations
   =============================== */

* {
  transition: all 0.3s ease-in-out;
}

/* Smooth scrolling for all elements */
* {
  scroll-behavior: smooth;
}

/* Smooth transitions for common interactive elements */
button, a, .cursor-pointer {
  transition: all 0.2s ease-in-out;
}

.text-justify {
  text-align: justify;
}

/* Custom blob shape */
.clip-blob {
  clip-path: path(
    "M183.5,-162.2C222.4,-112.3,229.9,-36.9,212.7,33.4C195.4,103.8,153.4,168.9,93.6,197.6C33.8,226.2,-43.7,218.4,-109.6,185.3C-175.6,152.1,-229.9,93.5,-234.7,33.2C-239.5,-27,-194.9,-89,-139,-135.4C-83.1,-181.7,-16,-211.4,54.4,-214.8C124.8,-218.3,166.7,-195.1,183.5,-162.2Z"
  );
}
